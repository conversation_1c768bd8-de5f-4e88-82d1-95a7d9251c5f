import { Injectable } from '@nestjs/common';

/**
 * Service xử lý các chức năng liên quan đến chat
 */
@Injectable()
export class ChatService {
  //   private readonly logger = new Logger(ChatService.name);

  //   constructor(
  //     @InjectRepository(Agent)
  //     private readonly agentRepository: Repository<Agent>,
  //     @InjectRepository(AgentBase)
  //     private readonly agentBaseRepository: Repository<AgentBase>,
  //     @InjectRepository(AgentSystem)
  //     private readonly agentSystemRepository: Repository<AgentSystem>,
  //     @InjectRepository(AgentUser)
  //     private readonly agentUserRepository: Repository<AgentUser>,
  //     @InjectRepository(AgentRole)
  //     private readonly agentRoleRepository: Repository<AgentRole>,
  //     @InjectRepository(UserMultiAgent)
  //     private readonly userMultiAgentRepository: Repository<UserMultiAgent>,
  //   ) {}

  //   /**
  //    * Tạo run chat với agent admin
  //    * @param createRunChatDto DTO chứa thông tin để tạo run chat
  //    * @param employeeId ID của nhân viên
  //    * @returns Thông tin run chat đã tạo
  //    */
  //   async createRunWithAdminAgent(
  //     createRunChatDto: CreateRunChatDto,
  //     employeeId: number,
  //   ): Promise<RunChatResponseDto> {
  //     try {
  //       const { agent_id, message } = createRunChatDto;

  //       // Lấy thông tin agent base
  //       const agentBase = await this.agentBaseRepository.findOne({
  //         where: { id: agent_id },
  //       });

  //       if (!agentBase) {
  //         throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
  //       }

  //       // Lấy thông tin agent
  //       const agent = await this.agentRepository.findOne({
  //         where: { id: agent_id },
  //       });

  //       if (!agent) {
  //         throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
  //       }

  //       // Lấy thông tin agent system
  //       const agentSystem = await this.agentSystemRepository.findOne({
  //         where: { id: agent_id },
  //       });

  //       // Lấy danh sách agent role (agent con) của agent base
  //       const agentRoles = await this.agentRoleRepository.createQueryBuilder('agentRole')
  //         .where('agentRole.agent_system_id = :agentSystemId', { agentSystemId: agent_id })
  //         .getMany();

  //       // Tạo danh sách agent con
  //       const children: AgentInterface[] = [];

  //       for (const agentRole of agentRoles) {
  //         // Lấy thông tin agent của agent role
  //         const childAgent = await this.agentRepository.findOne({
  //           where: { id: agentRole.id },
  //         });

  //         if (childAgent) {
  //           children.push({
  //             user_id: employeeId,
  //             id: childAgent.id,
  //             name: childAgent.name,
  //             description: agentRole.description || undefined,
  //             model: {
  //               model_id: childAgent.'deprecated-model-id' || '',
  //               top_p: childAgent.modelConfig.top_p || 1,
  //               top_k: childAgent.modelConfig.top_k || 50,
  //               temperature: childAgent.modelConfig.temperature || 1,
  //               max_token: childAgent.modelConfig.max_tokens || 2000,
  //               provider: this.getProviderFromModelId(childAgent.'deprecated-model-id' || ''),
  //             },
  //             instruction: childAgent.instruction || '',
  //             mcp_config: agentRole.moduleMcpConfig,
  //           });
  //         }
  //       }

  //       // Tạo response
  //       const response: AgentInterface = {
  //         user_id: employeeId,
  //         id: agent.id,
  //         name: agent.name,
  //         description: agentSystem?.promptTask || undefined,
  //         model: {
  //           model_id: agent.'deprecated-model-id' || '',
  //           top_p: agent.modelConfig.top_p || 1,
  //           top_k: agent.modelConfig.top_k || 50,
  //           temperature: agent.modelConfig.temperature || 1,
  //           max_token: agent.modelConfig.max_tokens || 2000,
  //           provider: this.getProviderFromModelId(agent.'deprecated-model-id' || ''),
  //         },
  //         instruction: agent.instruction || '',
  //         children: children.length > 0 ? children : undefined,
  //       };

  //       return new RunChatResponseDto(response);
  //     } catch (error) {
  //       this.logger.error(`Lỗi khi tạo run chat với agent admin: ${error.message}`);
  //       if (error instanceof AppException) {
  //         throw error;
  //       }
  //       throw new AppException(CHAT_ERROR_CODES.RUN_CREATION_FAILED);
  //     }
  //   }

  //   /**
  //    * Tạo run chat với agent user
  //    * @param createRunChatDto DTO chứa thông tin để tạo run chat
  //    * @param userId ID của người dùng
  //    * @returns Thông tin run chat đã tạo
  //    */
  //   async createRunWithUserAgent(
  //     createRunChatDto: CreateRunChatDto,
  //     userId: number,
  //   ): Promise<RunChatResponseDto> {
  //     try {
  //       const { agent_id, message } = createRunChatDto;

  //       // Lấy thông tin agent user
  //       const agentUser = await this.agentUserRepository.findOne({
  //         where: { id: agent_id, userId },
  //       });

  //       if (!agentUser) {
  //         throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
  //       }

  //       // Lấy thông tin agent
  //       const agent = await this.agentRepository.findOne({
  //         where: { id: agent_id },
  //       });

  //       if (!agent) {
  //         throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
  //       }

  //       // Lấy danh sách agent con từ user_multi_agent
  //       const childrenWithDetails = await this.findChildrenWithDetailsByParentId(
  //         agent_id,
  //         userId,
  //       );

  //       // Tạo danh sách agent con
  //       const children: AgentInterface[] = [];

  //       for (const { agent: childAgent, agentUser: childAgentUser, relation } of childrenWithDetails) {
  //         children.push({
  //           user_id: userId,
  //           id: childAgent.id,
  //           name: childAgent.name,
  //           description: relation.prompt || undefined,
  //           model: {
  //             model_id: childAgent.'deprecated-model-id' || '',
  //             top_p: childAgent.modelConfig.top_p || 1,
  //             top_k: childAgent.modelConfig.top_k || 50,
  //             temperature: childAgent.modelConfig.temperature || 1,
  //             max_token: childAgent.modelConfig.max_tokens || 2000,
  //             provider: this.getProviderFromModelId(childAgent.'deprecated-model-id' || ''),
  //           },
  //           instruction: childAgent.instruction || '',
  //         });
  //       }

  //       // Tạo instruction tổng hợp từ profile và instruction
  //       let combinedInstruction = agent.instruction || '';

  //       // Thêm thông tin profile vào instruction nếu có
  //       if (agentUser.profile) {
  //         const profileStr = Object.entries(agentUser.profile)
  //           .filter(([_, value]) => value)
  //           .map(([key, value]) => `${key}: ${value}`)
  //           .join('\n');

  //         if (profileStr) {
  //           combinedInstruction = `Profile:\n${profileStr}\n\nInstruction:\n${combinedInstruction}`;
  //         }
  //       }

  //       // Tạo response
  //       const response: AgentInterface = {
  //         user_id: userId,
  //         id: agent.id,
  //         name: agent.name,
  //         description: undefined,
  //         model: {
  //           model_id: agent.'deprecated-model-id' || '',
  //           top_p: agent.modelConfig.top_p || 1,
  //           top_k: agent.modelConfig.top_k || 50,
  //           temperature: agent.modelConfig.temperature || 1,
  //           max_token: agent.modelConfig.max_tokens || 2000,
  //           provider: this.getProviderFromModelId(agent.'deprecated-model-id' || ''),
  //         },
  //         instruction: combinedInstruction,
  //         children: children.length > 0 ? children : undefined,
  //       };

  //       return new RunChatResponseDto(response);
  //     } catch (error) {
  //       this.logger.error(`Lỗi khi tạo run chat với agent user: ${error.message}`);
  //       if (error instanceof AppException) {
  //         throw error;
  //       }
  //       throw new AppException(CHAT_ERROR_CODES.RUN_CREATION_FAILED);
  //     }
  //   }

  //   /**
  //    * Xác định provider dựa trên model_id
  //    * @param modelId ID của model
  //    * @returns Provider tương ứng với model_id
  //    */
  //   private getProviderFromModelId(modelId: string): string {
  //     // Xác định provider dựa trên model_id
  //     if (modelId.startsWith('gpt-')) {
  //       return 'openai';
  //     } else if (modelId.startsWith('claude-')) {
  //       return 'anthropic';
  //     } else if (modelId.startsWith('gemini-')) {
  //       return 'google';
  //     } else if (modelId.startsWith('llama-')) {
  //       return 'meta';
  //     } else {
  //       // Mặc định là OpenAI nếu không xác định được
  //       return 'openai';
  //     }
  //   }

  //   /**
  //    * Tìm tất cả các agent con của một agent cha kèm theo thông tin chi tiết
  //    * @param parentAgentId ID của agent cha
  //    * @param userId ID của người dùng
  //    * @returns Danh sách các agent con với thông tin chi tiết
  //    */
  //   private async findChildrenWithDetailsByParentId(
  //     parentAgentId: string,
  //     userId: number,
  //   ): Promise<{ agent: Agent; agentUser: AgentUser; relation: UserMultiAgent }[]> {
  //     try {
  //       // Lấy danh sách các quan hệ
  //       const relations = await this.userMultiAgentRepository.find({
  //         where: { parentAgentId },
  //       });

  //       if (!relations.length) {
  //         return [];
  //       }

  //       // Lấy danh sách ID của các agent con
  //       const childIds = relations.map(relation => relation.childAgentId);

  //       // Lấy thông tin chi tiết của các agent con
  //       const agents = await this.agentRepository.find({
  //         where: { id: In(childIds), deletedAt: IsNull() },
  //       });

  //       // Lấy thông tin AgentUser của các agent con
  //       const agentUsers = await this.agentUserRepository.find({
  //         where: { id: In(childIds), userId },
  //       });

  //       // Kết hợp thông tin
  //       const result: { agent: Agent; agentUser: AgentUser; relation: UserMultiAgent }[] = [];
  //       for (const relation of relations) {
  //         const agent = agents.find(a => a.id === relation.childAgentId);
  //         const agentUser = agentUsers.find(au => au.id === relation.childAgentId);

  //         if (agent && agentUser) {
  //           result.push({ agent, agentUser, relation });
  //         }
  //       }

  //       return result;
  //     } catch (error) {
  //       this.logger.error(`Lỗi khi tìm agent con: ${error.message}`);
  //       throw error;
  //     }
  //   }
}