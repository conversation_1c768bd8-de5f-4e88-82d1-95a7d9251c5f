import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO để xóa nhiều vector store
 */
export class DeleteVectorStoresDto {
  @ApiProperty({
    description: 'Danh sách ID của các vector store cần xóa',
    example: ['vs_a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'vs_b2c3d4e5-f6a7-8901-bcde-f01234567890'],
    type: [String],
  })
  @IsArray({ message: 'storeIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một vector store ID' })
  @IsString({ each: true, message: 'Mỗi vector store ID phải là chuỗi' })
  @IsNotEmpty({ each: true, message: 'Vector store ID không được để trống' })
  storeIds: string[];
}
